"use client";

import { useMutation } from "@tanstack/react-query";
import confetti from "canvas-confetti";
import { AnimatePresence, motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { useTRPC } from "@/trpc/client";

// Adjust this import to your app

// --- Simple utilities ---
const slugify = (s: string) =>
  s
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/(^-|-$)/g, "");

const StepDot = ({ active, done }: { active?: boolean; done?: boolean }) => (
  <div
    className={[
      "h-2.5 w-2.5 rounded-full transition-all",
      done ? "bg-emerald-500" : active ? "bg-foreground" : "bg-foreground/40",
      active ? "scale-100" : done ? "scale-100" : "scale-75",
    ].join(" ")}
  />
);

export default function SetupPage() {
  const router = useRouter();
  const trpc = useTRPC();
  const [step, setStep] = useState<1 | 2 | 3>(1);
  const [name, setName] = useState("");
  const [slug, setSlug] = useState("");
  const [logo, setLogo] = useState("");
  const [touched, setTouched] = useState(false);

  const createFirst = useMutation(
    trpc.setup.createFirst.mutationOptions({
      onSuccess: () => {
        // celebratory confetti
        burst();
        setStep(3);
        // small delay then route to app root (gated layout will allow once setupCompleted)
        setTimeout(() => router.replace("/"), 1200);
      },
    }),
  );

  const nameError = useMemo(() => {
    if (!touched) return "";
    if (!name.trim()) return "Please enter an organization name.";
    if (name.trim().length < 2) return "Name is too short.";
    return "";
  }, [name, touched]);

  // useEffect(() => {
  //   if (!slug && name) setSlug(slugify(name));
  // }, [name, slug]);

  function submit() {
    setTouched(true);
    if (nameError) return;
    createFirst.mutate({
      name,
      slug: slug,
      logo: logo || undefined,
    });
  }

  function burst() {
    const end = Date.now() + 350;
    const frame = () => {
      confetti({
        particleCount: 40,
        startVelocity: 45,
        spread: 60,
        ticks: 60,
        scalar: 0.8,
        origin: { y: 0.35, x: 0.5 },
      });
      if (Date.now() < end) requestAnimationFrame(frame);
    };
    frame();
  }

  return (
    <div className="relative min-h-[100dvh] overflow-hidden">
      {/* Animated gradient background */}
      <motion.div
        aria-hidden
        className="pointer-events-none absolute inset-0 -z-10"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <div className="absolute -top-40 -left-40 h-[32rem] w-[32rem] rounded-full bg-[radial-gradient(circle_at_center,rgba(56,189,248,0.35),transparent_60%)] blur-3xl" />
        <div className="absolute -bottom-40 -right-40 h-[32rem] w-[32rem] rounded-full bg-[radial-gradient(circle_at_center,rgba(167,139,250,0.35),transparent_60%)] blur-3xl" />
        <motion.div
          className="absolute left-1/2 top-1/2 h-[26rem] w-[26rem] -translate-x-1/2 -translate-y-1/2 rounded-[40%] bg-[radial-gradient(circle_at_center,rgba(16,185,129,0.22),transparent_60%)] blur-2xl"
          animate={{ rotate: 360 }}
          transition={{ duration: 40, repeat: Infinity, ease: "linear" }}
        />
      </motion.div>

      {/* Floating shapes for subtle parallax */}
      <motion.div
        aria-hidden
        className="pointer-events-none absolute inset-0 -z-10"
        animate={{ y: [0, -10, 0] }}
        transition={{ duration: 10, repeat: Infinity, ease: "easeInOut" }}
      >
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute h-2 w-2 rounded-full bg-white/40"
            style={{ top: `${(i * 9) % 100}%`, left: `${(i * 17) % 100}%` }}
            animate={{ y: [0, -8, 0] }}
            transition={{
              duration: 6 + (i % 5),
              repeat: Infinity,
              delay: i * 0.15,
            }}
          />
        ))}
      </motion.div>

      {/* Container */}
      <div className="mx-auto flex max-w-xl flex-col items-center px-6 py-12">
        {/* Header */}
        <motion.header
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8 text-center"
        >
          <Badge variant="secondary" className="backdrop-blur">
            Welcome <span className="mx-1">•</span> Let’s set up your workspace
          </Badge>
          <h1 className="mt-3 text-3xl font-semibold tracking-tight drop-shadow md:text-4xl">
            Create your first organization
          </h1>
          <p className="mt-2 max-w-md text-balance">
            We’ll use this to organize your team, projects, and permissions. You
            can change these later.
          </p>
        </motion.header>

        {/* Stepper */}
        <div className="mb-6 flex items-center gap-3">
          <StepDot active={step === 1} done={step > 1} />
          <div className="h-px w-16 bg-white/30" />
          <StepDot active={step === 2} done={step > 2} />
          <div className="h-px w-16 bg-white/30" />
          <StepDot active={step === 3} done={step > 3} />
        </div>

        {/* Card */}
        <motion.div layout className="w-full">
          <AnimatePresence mode="wait">
            {step === 1 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, y: 8 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -8 }}
                transition={{ duration: 0.25 }}
              >
                <Card className="backdrop-blur-md shadow-xl">
                  <CardHeader>
                    <CardTitle>Welcome 👋</CardTitle>
                    <CardDescription>
                      Let’s create your organization. You’ll be set as{" "}
                      <span className="font-medium ">Owner</span>.
                    </CardDescription>
                  </CardHeader>
                  <CardFooter className="flex justify-end">
                    <Button onClick={() => setStep(2)} variant="secondary">
                      Get started
                    </Button>
                  </CardFooter>
                </Card>
              </motion.div>
            )}

            {step === 2 && (
              <motion.div
                key="step2"
                initial={{ opacity: 0, y: 8 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -8 }}
                transition={{ duration: 0.25 }}
              >
                <Card className="backdrop-blur-md shadow-xl">
                  <CardHeader>
                    <CardTitle>Organization details</CardTitle>
                    <CardDescription>
                      You can change these later in settings.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-1.5">
                      <Label htmlFor="org-name">Name</Label>
                      <Input
                        id="org-name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        onBlur={() => setTouched(true)}
                        placeholder="e.g. Acme Inc"
                      />
                      {nameError && (
                        <p className="text-xs text-red-300">{nameError}</p>
                      )}
                    </div>

                    <div className="space-y-1.5">
                      <Label htmlFor="org-slug">Slug (optional)</Label>
                      <Input
                        id="org-slug"
                        value={slug}
                        onChange={(e) => setSlug(slugify(e.target.value))}
                        placeholder="acme"
                      />
                      <p className="text-xs ">
                        URL:{" "}
                        <span className="font-mono ">
                          /org/{slug || slugify(name) || "your-org"}
                        </span>
                      </p>
                    </div>

                    <div className="space-y-1.5">
                      <Label htmlFor="org-logo">Logo URL (optional)</Label>
                      <Input
                        id="org-logo"
                        value={logo}
                        onChange={(e) => setLogo(e.target.value)}
                        placeholder="https://..."
                      />
                    </div>
                  </CardContent>
                  <Separator />
                  <CardFooter className="flex items-center justify-between">
                    <Button variant="ghost" onClick={() => setStep(1)}>
                      Back
                    </Button>
                    <Button onClick={submit} disabled={createFirst.isPending}>
                      {createFirst.isPending && (
                        <motion.span
                          className="mr-2 inline-block h-4 w-4 rounded-full border-2 border-emerald-900/30 border-b-transparent"
                          animate={{ rotate: 360 }}
                          transition={{
                            repeat: Infinity,
                            duration: 0.6,
                            ease: "linear",
                          }}
                        />
                      )}
                      Create organization
                    </Button>
                  </CardFooter>
                </Card>
              </motion.div>
            )}

            {step === 3 && (
              <motion.div
                key="step3"
                initial={{ opacity: 0, y: 8 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -8 }}
                transition={{ duration: 0.25 }}
              >
                <Card className="border-white/15 bg-white/10 text-white backdrop-blur-md shadow-xl">
                  <CardContent className="flex flex-col items-center gap-3 py-8 text-center">
                    <motion.div
                      initial={{ scale: 0.9, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{
                        type: "spring",
                        stiffness: 220,
                        damping: 18,
                      }}
                      className="grid h-16 w-16 place-items-center rounded-2xl bg-emerald-400/90 text-emerald-950 shadow-xl"
                    >
                      <Check className="h-7 w-7" />
                    </motion.div>
                    <h2 className="text-xl font-medium text-white">All set!</h2>
                    <p className="max-w-sm text-white/80">
                      Your organization is ready. We’re taking you to your
                      dashboard…
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* Tiny footer */}
        <div className="mt-6 text-center text-xs text-white/60">
          You can update these details later in Settings.
        </div>
      </div>

      {/* Global styles for dark gradient body */}
      <style jsx global>{`
        html, body { height: 100%; }
        body { background: radial-gradient(60% 80% at 50% 0%, #0a0f14 0%, #07090c 45%, #05070b 100%); }
      `}</style>
    </div>
  );
}

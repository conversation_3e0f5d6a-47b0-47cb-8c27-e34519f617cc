"use client";

import { Building2, Check, ChevronsUpDown, Plus } from "lucide-react";
import type { FC } from "react";
import * as React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { authClient } from "@/lib/auth-client";
import { cn } from "@/lib/utils";

/**
 * A polished Organization Switcher built with shadcn/ui.
 *
 * Assumptions about your auth client:
 * - Listing orgs: authClient.useListOrganizations()
 * - Active org:   authClient.useActiveOrganization()
 * - Switch org:   authClient.setActiveOrganization?(orgId) OR authClient.switchOrganization?(orgId)
 *
 * If your API differs, adjust the `switchToOrg` function accordingly.
 */

export type OrgLike = {
  id: string;
  name?: string | null;
  slug?: string | null;
  image?: string | null;
};

export interface OrganizationSwitcherProps
  extends React.ComponentPropsWithoutRef<typeof Button> {
  /**
   * Optional callback after a successful switch.
   */
  onSwitched?: (org: OrgLike) => void;
  /**
   * Optional handler to create a new organization.
   * If not provided, falls back to navigating to "/organizations/new".
   */
  onCreateNew?: () => void;
  /**
   * Control whether the "Create organization" action is shown.
   */
  allowCreate?: boolean;
}

export const OrganizationSwitcher: FC<OrganizationSwitcherProps> = ({
  className,
  size = "default",
  variant = "outline",
  onSwitched,
  onCreateNew,
  allowCreate = true,
  ...buttonProps
}) => {
  const [open, setOpen] = React.useState(false);

  const {
    data: organizations,
    isPending: isListPending,
    error: listError,
  } = authClient.useListOrganizations();

  const {
    data: activeOrg,
    isPending: isActivePending,
    error: activeError,
  } = authClient.useActiveOrganization();

  const {
    data: session,
    isPending: isSessionPending,
    error: sessionError,
  } = authClient.useSession();

  const loading = isListPending || isActivePending || isSessionPending;
  const hasError = Boolean(listError || activeError || sessionError);

  const switchToOrg = React.useCallback(
    async (org: OrgLike) => {
      try {
        // Prefer a method on your auth client. Adjust as needed for your setup.
        const anyClient = authClient as any;
        if (typeof anyClient.setActiveOrganization === "function") {
          await anyClient.setActiveOrganization(org.id);
        } else if (typeof anyClient.switchOrganization === "function") {
          await anyClient.switchOrganization(org.id);
        } else if (typeof anyClient.activateOrganization === "function") {
          await anyClient.activateOrganization(org.id);
        } else {
          throw new Error(
            "No switching method found on authClient. Please wire this up.",
          );
        }
        onSwitched?.(org);
        // toast({
        //   title: "Organization switched",
        //   description: `You're now working in ${org.name || org.slug || "this org"}.`,
        // });
        setOpen(false);
      } catch (err: any) {
        console.error(err);
        // toast({
        //   title: "Could not switch organization",
        //   description: err?.message ?? "Please try again.",
        //   variant: "destructive",
        // });
      }
    },
    [onSwitched],
  );

  const orgs: OrgLike[] = React.useMemo(() => {
    // Normalize a few possible shapes just in case
    const list = Array.isArray(organizations) ? organizations : organizations;
    return (list ?? []).map((o: any) => ({
      id: String(o.id ?? o.orgId ?? o._id),
      name: o.name ?? o.displayName ?? null,
      slug: o.slug ?? o.handle ?? null,
      image: o.image ?? o.logoUrl ?? o.avatarUrl ?? null,
    }));
  }, [organizations]);

  const current: OrgLike | null = activeOrg
    ? {
        id: String(
          (activeOrg as any).id ??
            (activeOrg as any).orgId ??
            (activeOrg as any)._id,
        ),
        name: (activeOrg as any).name ?? (activeOrg as any).displayName ?? null,
        slug: (activeOrg as any).slug ?? (activeOrg as any).handle ?? null,
        image:
          (activeOrg as any).image ??
          (activeOrg as any).logoUrl ??
          (activeOrg as any).avatarUrl ??
          null,
      }
    : null;

  const userDisplay = session?.user?.name ?? session?.user?.email ?? "You";

  if (loading) {
    return (
      <Button
        variant={variant}
        size={size}
        className={cn("w-56 justify-start", className)}
        disabled
      >
        <Skeleton className="h-5 w-5 rounded-full mr-2" />
        <Skeleton className="h-4 w-32" />
      </Button>
    );
  }

  if (hasError) {
    return (
      <Button
        variant="destructive"
        size={size}
        className={cn("w-56 justify-between", className)}
        {...buttonProps}
        disabled
      >
        <span className="inline-flex items-center gap-2">
          <Building2 className="h-4 w-4" />
          Organization error
        </span>
        <ChevronsUpDown className="h-4 w-4 opacity-50" />
      </Button>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={variant}
          size={size}
          role="combobox"
          aria-expanded={open}
          aria-label="Select organization"
          className={cn("w-56 justify-between", className)}
          {...buttonProps}
        >
          <span className="inline-flex items-center gap-2 truncate">
            <OrgAvatar
              name={current?.name ?? current?.slug ?? "Org"}
              src={current?.image ?? undefined}
            />
            <span className="truncate text-left">
              {current?.name || current?.slug || "Select organization"}
            </span>
          </span>
          <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-72 p-0" align="start">
        <Command>
          <CommandInput placeholder={`Search organizations…`} />
          <CommandList>
            <CommandEmpty>No organizations found.</CommandEmpty>

            <CommandGroup heading="Your organizations">
              <ScrollArea className="h-64">
                {orgs.map((org) => (
                  <CommandItem key={org.id} onSelect={() => switchToOrg(org)}>
                    <div className="mr-2 flex items-center">
                      <OrgAvatar
                        name={org.name ?? org.slug ?? "Org"}
                        src={org.image ?? undefined}
                      />
                    </div>
                    <div className="flex grow flex-col truncate">
                      <span className="truncate">{org.name || org.slug}</span>
                      {org.slug && (
                        <span className="text-xs text-muted-foreground truncate">
                          {org.slug}
                        </span>
                      )}
                    </div>
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        current?.id === org.id ? "opacity-100" : "opacity-0",
                      )}
                    />
                  </CommandItem>
                ))}
              </ScrollArea>
            </CommandGroup>

            {allowCreate && (
              <>
                <CommandSeparator />
                <CommandGroup>
                  <CommandItem
                    onSelect={() => {
                      setOpen(false);
                      if (onCreateNew) return onCreateNew();
                      // Default: navigate to your new org page.
                      window.location.href = "/organizations/new";
                    }}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Create organization
                  </CommandItem>
                </CommandGroup>
              </>
            )}

            <Separator className="my-1" />
            <CommandGroup heading="Signed in as">
              <div className="px-3 pb-3 text-sm text-muted-foreground truncate">
                {userDisplay}
              </div>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

/**
 * Avatar that shows an image (if available) or else initials.
 */
const OrgAvatar: FC<{ name: string; src?: string }> = ({ name, src }) => {
  return (
    <Avatar className="h-5 w-5">
      {src ? <AvatarImage src={src} alt={name} /> : null}
      <AvatarFallback className="text-[10px]">
        {getInitials(name)}
      </AvatarFallback>
    </Avatar>
  );
};

function getInitials(name?: string) {
  if (!name) return "?";
  const parts = String(name).trim().split(/\s+/).slice(0, 2);
  return parts.map((p) => p[0]?.toUpperCase()).join("") || "?";
}

export default OrganizationSwitcher;

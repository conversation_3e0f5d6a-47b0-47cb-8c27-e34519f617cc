import { eq } from "drizzle-orm";
import z from "zod";
import { db } from "@/db";
import { user } from "@/db/schema";
import { auth } from "@/lib/auth";
import { createTRPCRouter, protectedUserProcedure } from "@/trpc/init";

export const setupRouter = createTRPCRouter({
  createFirst: protectedUserProcedure
    .input(
      z.object({
        name: z.string().min(2).max(80),
        slug: z
          .string()
          .min(2)
          .max(80)
          .regex(/^[a-z0-9-]+$/),
        logo: z.string().url().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      try {
        if (!ctx.auth?.user.id) {
          throw new Error("Unauthorized");
        }

        if (ctx.auth.user.setupCompleted) {
          throw new Error("Setup already completed");
        }

        const org = await auth.api.createOrganization({
          body: {
            name: input.name,
            slug: input.slug,
            logo: input.logo,
            keepCurrentActiveOrganization: false,
          },
        });

        await db
          .update(user)
          .set({ setupCompleted: true })
          .where(eq(user.id, ctx.auth.user.id));

        return org?.slug;
      } catch (error) {
        console.log(error);
        throw error;
      }
    }),
});
